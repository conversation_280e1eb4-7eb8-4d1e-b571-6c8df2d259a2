#!/usr/bin/env python3
"""
Demo script for SAE Activation Classifier.

This script demonstrates how to use the SAEActivationClassifier to detect
behavioral conflicts using SAE feature activations.
"""

import torch
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import ITAS components
from itas.core.sae import SAE
from itas.analysis.sae_activation_classifier import (
    SAEActivationClassifier,
    TrainingConfig,
    create_classifier_from_sae,
    train_conflict_classifier,
)


def generate_synthetic_data(n_samples: int = 100, hidden_dim: int = 4096) -> tuple:
    """
    Generate synthetic activation data for demonstration.
    
    Args:
        n_samples: Number of samples per class
        hidden_dim: Hidden dimension size
        
    Returns:
        Tuple of (conflict_activations, no_conflict_activations)
    """
    logger.info(f"Generating synthetic data: {n_samples} samples per class")
    
    # Generate conflict activations (higher variance in certain dimensions)
    conflict_base = torch.randn(n_samples, hidden_dim) * 0.5
    conflict_signal = torch.zeros(n_samples, hidden_dim)
    conflict_signal[:, :100] = torch.randn(n_samples, 100) * 2.0  # Strong signal in first 100 dims
    conflict_activations = conflict_base + conflict_signal
    
    # Generate no-conflict activations (more uniform)
    no_conflict_activations = torch.randn(n_samples, hidden_dim) * 0.8
    
    return conflict_activations, no_conflict_activations


def demo_basic_usage():
    """Demonstrate basic classifier usage with synthetic data."""
    logger.info("=== Basic Usage Demo ===")
    
    # Load a pre-trained SAE (you'll need to adjust this path)
    sae_path = "./sae_models/llama_3_1_8b_layer15_gated_sae.pt"
    
    if not Path(sae_path).exists():
        logger.warning(f"SAE model not found at {sae_path}. Using synthetic SAE for demo.")
        # Create a simple mock SAE for demonstration
        from itas.core.sae import SAE
        sae = SAE(d_in=4096, d_sae=131072, device="cuda" if torch.cuda.is_available() else "cpu")
    else:
        sae = SAE.load(sae_path)
    
    # Generate synthetic data
    conflict_acts, no_conflict_acts = generate_synthetic_data(n_samples=200)
    
    # Create classifier
    config = TrainingConfig(
        learning_rate=0.001,
        num_epochs=50,
        batch_size=32,
        l1_coefficient=1e-4,
        early_stopping_patience=5
    )
    
    classifier = SAEActivationClassifier(sae, config)
    
    # Train classifier
    logger.info("Training classifier...")
    result = classifier.train(conflict_acts, no_conflict_acts)
    
    logger.info(f"Training Results:")
    logger.info(f"  Accuracy: {result.accuracy:.4f}")
    logger.info(f"  AUC: {result.auc:.4f}")
    logger.info(f"  AUPRC: {result.auprc:.4f}")
    logger.info(f"  Precision: {result.precision:.4f}")
    logger.info(f"  Recall: {result.recall:.4f}")
    logger.info(f"  F1 Score: {result.f1_score:.4f}")
    
    # Test on new data
    test_conflict, test_no_conflict = generate_synthetic_data(n_samples=50)
    test_activations = torch.cat([test_conflict, test_no_conflict], dim=0)
    test_labels = torch.cat([torch.ones(50), torch.zeros(50)])
    
    test_result = classifier.evaluate(test_activations, test_labels)
    logger.info(f"Test Results:")
    logger.info(f"  Test Accuracy: {test_result.accuracy:.4f}")
    logger.info(f"  Test AUC: {test_result.auc:.4f}")
    
    # Get feature importance
    importance_info = classifier.get_feature_importance(top_k=20)
    logger.info(f"Feature Importance:")
    logger.info(f"  Mean importance: {importance_info['mean_importance']:.6f}")
    logger.info(f"  Sparsity: {importance_info['sparsity']:.4f}")
    logger.info(f"  Top 5 features: {importance_info['top_features'][:5]}")
    
    return classifier, result


def demo_cross_validation():
    """Demonstrate cross-validation."""
    logger.info("\n=== Cross-Validation Demo ===")
    
    # Load SAE (same as above)
    sae_path = "./sae_models/llama_3_1_8b_layer15_gated_sae.pt"
    
    if not Path(sae_path).exists():
        from itas.core.sae import SAE
        sae = SAE(d_in=4096, d_sae=131072, device="cuda" if torch.cuda.is_available() else "cpu")
    else:
        sae = SAE.load(sae_path)
    
    # Generate larger dataset for CV
    conflict_acts, no_conflict_acts = generate_synthetic_data(n_samples=500)
    
    # Use utility function for training with CV
    config = TrainingConfig(num_epochs=30, batch_size=64)
    classifier, cv_results = train_conflict_classifier(
        sae, conflict_acts, no_conflict_acts, config, cross_validate=True, n_folds=5
    )
    
    # Summarize CV results
    cv_summary = SAEActivationClassifier.summarize_cv_results(cv_results)
    
    logger.info("Cross-Validation Results:")
    for metric in ["accuracy", "auc", "auprc", "f1_score"]:
        mean_val = cv_summary[f"{metric}_mean"]
        std_val = cv_summary[f"{metric}_std"]
        logger.info(f"  {metric.upper()}: {mean_val:.4f} ± {std_val:.4f}")
    
    return classifier, cv_results


def demo_save_load():
    """Demonstrate saving and loading classifier."""
    logger.info("\n=== Save/Load Demo ===")
    
    # Train a simple classifier
    from itas.core.sae import SAE
    sae = SAE(d_in=4096, d_sae=131072, device="cuda" if torch.cuda.is_available() else "cpu")
    
    conflict_acts, no_conflict_acts = generate_synthetic_data(n_samples=100)
    
    classifier = SAEActivationClassifier(sae)
    result = classifier.train(conflict_acts, no_conflict_acts)
    
    # Save classifier
    save_path = "./demo_classifier.pt"
    classifier.save_model(save_path)
    logger.info(f"Saved classifier to {save_path}")
    
    # Load classifier
    new_classifier = SAEActivationClassifier(sae)
    new_classifier.load_model(save_path)
    logger.info(f"Loaded classifier from {save_path}")
    
    # Test that loaded classifier works
    test_acts, _ = generate_synthetic_data(n_samples=10)
    predictions, probabilities = new_classifier.predict_from_activations(test_acts)
    logger.info(f"Predictions shape: {predictions.shape}")
    logger.info(f"Sample predictions: {predictions[:5]}")
    
    # Clean up
    Path(save_path).unlink(missing_ok=True)
    
    return new_classifier


def main():
    """Run all demos."""
    logger.info("Starting SAE Activation Classifier Demo")
    
    try:
        # Basic usage demo
        classifier1, result1 = demo_basic_usage()
        
        # Cross-validation demo
        classifier2, cv_results = demo_cross_validation()
        
        # Save/load demo
        classifier3 = demo_save_load()
        
        logger.info("\n=== Demo completed successfully! ===")
        
    except Exception as e:
        logger.error(f"Demo failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
