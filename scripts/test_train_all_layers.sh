#!/bin/bash

# Test script for train_all_layers_sae.sh
# This script tests the GPU assignment logic without actually training SAEs

echo "🧪 Testing train_all_layers_sae.sh GPU assignment logic"
echo "=" * 60

# Configuration (same as main script)
gpus=(0 1 2 3)
num_gpus=${#gpus[@]}
num_layers=8  # Test with fewer layers for quick verification

echo "Configuration:"
echo "  Available GPUs: ${gpus[@]}"
echo "  Number of GPUs: $num_gpus"
echo "  Test layers: $num_layers"
echo ""

# Test the assignment logic
echo "Testing GPU assignment logic:"
echo ""

for ((batch_start_layer_idx=0; batch_start_layer_idx<num_layers; batch_start_layer_idx+=num_gpus)); do
    echo "Batch starting at layer $batch_start_layer_idx:"
    
    for ((gpu_array_idx=0; gpu_array_idx<num_gpus; gpu_array_idx++)); do
        current_layer_to_process=$((batch_start_layer_idx + gpu_array_idx))
        
        if [ $current_layer_to_process -lt $num_layers ]; then
            assigned_gpu_id=${gpus[gpu_array_idx]}
            
            echo "  Layer $current_layer_to_process -> GPU $assigned_gpu_id"
            
            # Test the CUDA_VISIBLE_DEVICES assignment
            echo "    CUDA_VISIBLE_DEVICES=$assigned_gpu_id python test_single_gpu_assignment.py"
            
            # Actually test the GPU assignment (if test script exists)
            if [ -f "test_single_gpu_assignment.py" ]; then
                echo "    Testing GPU $assigned_gpu_id assignment..."
                CUDA_VISIBLE_DEVICES=$assigned_gpu_id python test_single_gpu_assignment.py
                if [ $? -eq 0 ]; then
                    echo "    ✅ GPU $assigned_gpu_id assignment test PASSED"
                else
                    echo "    ❌ GPU $assigned_gpu_id assignment test FAILED"
                fi
            else
                echo "    ⚠️  test_single_gpu_assignment.py not found, skipping actual test"
            fi
            echo ""
        fi
    done
    
    echo "  Batch complete. In real training, would wait for all processes to finish."
    echo ""
done

echo "🎉 GPU assignment logic test complete!"
echo ""
echo "Expected behavior:"
echo "  - Each layer should be assigned to exactly one GPU"
echo "  - GPUs should be used in round-robin fashion"
echo "  - No layer should be assigned to multiple GPUs"
echo "  - All available GPUs should be utilized"
echo ""
echo "To run actual SAE training:"
echo "  ./train_all_layers_sae.sh"
