#!/usr/bin/env python3
"""
Test script to verify single GPU assignment works correctly.

This script tests that when CUDA_VISIBLE_DEVICES is set to a single GPU,
the SAE training script correctly uses only that GPU.

Usage:
    CUDA_VISIBLE_DEVICES=0 python test_single_gpu_assignment.py
    CUDA_VISIBLE_DEVICES=1 python test_single_gpu_assignment.py
    CUDA_VISIBLE_DEVICES=2 python test_single_gpu_assignment.py
    CUDA_VISIBLE_DEVICES=3 python test_single_gpu_assignment.py
"""

import os
import torch
import sys

def test_gpu_assignment():
    """Test that GPU assignment works as expected."""
    
    print("🧪 Testing Single GPU Assignment")
    print("=" * 50)
    
    # Check environment
    cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')
    print(f"CUDA_VISIBLE_DEVICES: {cuda_visible}")
    
    if cuda_visible == 'Not set':
        print("❌ CUDA_VISIBLE_DEVICES not set!")
        print("   Please run with: CUDA_VISIBLE_DEVICES=X python test_single_gpu_assignment.py")
        return False
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("❌ CUDA not available!")
        return False
    
    # Check device count
    device_count = torch.cuda.device_count()
    print(f"Visible GPU count: {device_count}")
    
    if device_count != 1:
        print(f"❌ Expected 1 GPU, but found {device_count}")
        print("   Single GPU assignment failed!")
        return False
    
    # Test GPU properties
    device_name = torch.cuda.get_device_name(0)
    device_props = torch.cuda.get_device_properties(0)
    total_memory = device_props.total_memory / 1e9
    
    print(f"✓ GPU 0: {device_name}")
    print(f"✓ VRAM: {total_memory:.1f} GB")
    
    # Test tensor operations
    try:
        # Create a test tensor on GPU
        test_tensor = torch.randn(1000, 1000, device='cuda:0')
        result = torch.matmul(test_tensor, test_tensor.T)
        
        print(f"✓ GPU operations working")
        print(f"✓ Test tensor device: {test_tensor.device}")
        print(f"✓ Result tensor device: {result.device}")
        
        # Check memory usage
        memory_allocated = torch.cuda.memory_allocated(0) / 1e6
        print(f"✓ GPU memory allocated: {memory_allocated:.1f} MB")
        
    except Exception as e:
        print(f"❌ GPU operations failed: {e}")
        return False
    
    print("\n🎉 Single GPU assignment test PASSED!")
    print(f"   GPU {cuda_visible} is correctly assigned and functional")
    
    return True

def test_sae_training_config():
    """Test SAE training configuration with single GPU."""
    
    print("\n🔧 Testing SAE Training Configuration")
    print("=" * 50)
    
    try:
        # Import SAE training components
        from sae_train import detect_gpu_setup, create_config
        
        # Test GPU detection
        device, num_gpus = detect_gpu_setup()
        
        if num_gpus != 1:
            print(f"❌ Expected 1 GPU in SAE training, but detected {num_gpus}")
            return False
        
        print(f"✓ SAE training detects {num_gpus} GPU correctly")
        
        # Test configuration creation
        class MockArgs:
            def __init__(self):
                self.model_name = "meta-llama/Llama-3.1-8B-Instruct"
                self.dataset_name = "togethercomputer/RedPajama-Data-1T-Sample"
                self.architecture = "gated"
                self.expansion_factor = 32
                self.hook_layer = 16
                self.hook_point = "mlp_out"
                self.hook_name = None
                self.total_tokens = 1000000  # Small for testing
                self.batch_size = 1024  # Small for testing
                self.learning_rate = 3e-4
                self.l1_coefficient = 1e-3
                self.wandb_project = "test"
                self.wandb_run_name = "test"
                self.no_wandb = True  # Disable for testing
        
        args = MockArgs()
        config, _ = create_config(num_gpus=1, args=args)
        
        print(f"✓ Configuration created successfully")
        print(f"✓ Device mapping: {config.model.device_map}")
        print(f"✓ Batch size: {config.training.batch_size}")
        print(f"✓ SAE device: {config.device}")
        
        # Verify single GPU configuration
        if config.model.device_map != {"": "cuda:0"}:
            print(f"❌ Expected device_map={{'': 'cuda:0'}}, got {config.model.device_map}")
            return False
        
        if config.training.batch_size != args.batch_size:
            print(f"❌ Batch size was scaled unexpectedly: {args.batch_size} -> {config.training.batch_size}")
            return False
        
        print("\n🎉 SAE training configuration test PASSED!")
        
    except Exception as e:
        print(f"❌ SAE training configuration test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    
    print("🚀 Single GPU Assignment Test Suite")
    print("=" * 60)
    
    # Test 1: Basic GPU assignment
    test1_passed = test_gpu_assignment()
    
    # Test 2: SAE training configuration
    test2_passed = test_sae_training_config()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"  Basic GPU Assignment: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"  SAE Training Config:  {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests PASSED! Single GPU assignment is working correctly.")
        return 0
    else:
        print("\n❌ Some tests FAILED! Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
