"""
Analysis and evaluation tools for ITAS.

This module provides comprehensive tools for analyzing SAE features,
extracting functions, and performing representation engineering.
"""

from .activation_analyzer import ActivationAnalyzer, ActivationStats
from .representation_engineer import (
    KnowledgeSelectionSteering,
    RepresentationEngineer,
    MutualInformationResult,
    FunctionalActivations,
    KnowledgeSteeringResult,
)
from .evaluation import SAEEvaluator, SAEEvaluationResult, InterventionEvaluationResult
from .visualization import SAEVisualizer

__all__ = [
    # Main classes
    "ActivationAnalyzer",
    "RepresentationEngineer",
    "KnowledgeSelectionSteering",
    "SAEEvaluator",
    "SAEVisualizer",
    # Result classes
    "ActivationStats",
    "MutualInformationResult",
    "FunctionalActivations",
    "KnowledgeSteeringResult",
    "SAEEvaluationResult",
    "InterventionEvaluationResult",
    "ClassificationResult",
    # Classifier components
    "LogisticRegressionClassifier",
    "TrainingConfig",
    # Utility functions
    "create_classifier_from_sae",
    "train_conflict_classifier",
]
