# Knowledge Selection Steering

This document describes the Knowledge Selection Steering approach implemented in ITAS, which uses mutual information to identify SAE features responsible for driving knowledge selection behaviors and applies targeted interventions to control contextual vs parametric knowledge usage.

## Overview

Traditional representation engineering approaches add steering vectors naively. In contrast, Knowledge Selection Steering implements a principled approach based on mutual information analysis to identify the specific SAE activations that drive knowledge selection behaviors.

## Methodology

### 1. Mutual Information Calculation

The first step calculates mutual information between each SAE activation and knowledge selection behaviors:

```
I(Z_i; Y)
```

Where:
- `Z_i` is the i-th activation of the SAE
- `Y = {C, M}` represents the generated answers (Contextual vs Parametric knowledge)

Higher `I(Z_i; Y)` indicates higher dependency between `Z_i` and knowledge selection behavior.

### 2. Feature Selection

We select the top-k activations with the highest `I(Z_i; Y)`, denoted as `Z`.

### 3. Knowledge Correlation Analysis

For each selected feature `Z_i ∈ Z`, we determine which knowledge selection behavior it positively correlates with:

- Calculate expected values: `E_C[Z_i]` and `E_M[Z_i]`
- If `E_C[Z_i] - E_M[Z_i] > 0`: `Z_i` is positively correlated with contextual knowledge
- Otherwise: `Z_i` is positively correlated with parametric knowledge

### 4. Functional Activation Construction

We construct two functional SAE activations:
- `z_C`: steers usage of contextual knowledge
- `z_M`: steers usage of parametric knowledge

For each element:
- Set to 0 if `Z_i ∉ Z` (not in selected features)
- Otherwise, set based on expectations from step 3

### 5. Knowledge Selection Steering

To control knowledge usage at inference time, we apply the following equations:

**Equation (2) - Determine removal values:**
```
z^-_i = min{z_i, z^C_i}
```

**Equation (3) - Determine addition values:**
```
z^+_i = max{z^M_i - z_i, 0}
```

**Equation (4) - Apply intervention:**
```
h' = h + α(-g_φ(z^-) + g_φ(z^+))
```

Where:
- `h` is the original hidden state
- `z_i` is the current SAE activation
- `α` is a user-defined hyperparameter controlling intervention strength
- `g_φ` is the SAE decoder

## Usage

### Basic Example

```python
from itas.analysis.representation_engineer import KnowledgeSelectionSteering
from itas.core.sae import SAE

# Load your SAE
sae = SAE.load("path/to/your/sae")

# Initialize steering
steering = KnowledgeSelectionSteering(
    sae=sae,
    device="cuda",
    num_processes=4,
)

# Calculate mutual information
mi_result = steering.calculate_mutual_information(
    contextual_hiddens=contextual_hidden_states,
    parametric_hiddens=parametric_hidden_states,
    top_k_proportion=0.1,  # Select top 10% of features
)

# Create functional activations
functional_activations = steering.create_functional_activations(mi_result)

# Apply steering
steered_hiddens = steering.apply_knowledge_steering(
    hidden_states=test_hiddens,
    steering_direction="parametric",  # or "contextual"
    alpha=1.0,
    functional_activations=functional_activations,
)
```

### Model Integration

```python
# Create intervention function for model hooks
intervention_fn = steering.create_intervention_function(
    steering_direction="parametric",
    alpha=1.0,
    functional_activations=functional_activations,
)

# Apply to model layer
target_layer = model.model.layers[16]  # Example layer
hook_handle = target_layer.register_forward_hook(intervention_fn)

# Generate with intervention
output = model.generate(input_ids, max_new_tokens=50)

# Clean up
hook_handle.remove()
```

### Saving and Loading Results

```python
# Save results
steering.save_results(
    save_path="steering_results.pt",
    mi_result=mi_result,
    functional_activations=functional_activations,
)

# Load results
loaded_steering, loaded_mi, loaded_functional = KnowledgeSelectionSteering.load_results(
    load_path="steering_results.pt",
    sae=sae,
    device="cuda",
)
```

## Key Advantages

1. **Principled Feature Selection**: Uses mutual information rather than arbitrary feature selection
2. **Targeted Interventions**: Only modifies features that actually drive knowledge selection
3. **Constrained Modifications**: Ensures non-negative activations and prevents excessive changes
4. **Bidirectional Control**: Can steer toward either contextual or parametric knowledge
5. **Strength Control**: Hyperparameter α allows fine-tuning intervention strength

## Parameters

### `calculate_mutual_information()`

- `contextual_hiddens`: Hidden states when model uses contextual knowledge
- `parametric_hiddens`: Hidden states when model uses parametric knowledge  
- `top_k`: Number of top features to select
- `top_k_proportion`: Proportion of top features to select (alternative to top_k)
- `minmax_normalization`: Whether to apply MinMax normalization
- `equal_label_examples`: Whether to balance examples between labels
- `seed`: Random seed for reproducibility

### `apply_knowledge_steering()`

- `hidden_states`: Input hidden states to modify
- `steering_direction`: "contextual" or "parametric"
- `alpha`: Steering strength (higher = stronger intervention)
- `functional_activations`: Functional activations from `create_functional_activations()`

## Implementation Details

The implementation follows the exact equations from the paper:

1. **Non-negative constraint**: `z^-_i = min{z_i, z^C_i}` ensures activations remain non-negative after removal
2. **No excess addition**: `z^+_i = max{z^M_i - z_i, 0}` prevents adding beyond the target activation level
3. **Flexible control**: The α parameter allows users to control intervention strength
4. **Efficient computation**: Uses multiprocessing for mutual information calculation

## Comparison with Traditional Approaches

| Aspect | Traditional Steering | Knowledge Selection Steering |
|--------|---------------------|----------------------------|
| Feature Selection | Manual or heuristic | Mutual information based |
| Intervention Method | Naive vector addition | Constrained activation editing |
| Knowledge Awareness | Generic | Knowledge-type specific |
| Theoretical Foundation | Limited | Information-theoretic |
| Controllability | Basic strength parameter | Multiple constraints + strength |

## Best Practices

1. **Data Quality**: Ensure high-quality contextual vs parametric hidden states
2. **Feature Selection**: Start with 5-10% of features, adjust based on results
3. **Intervention Strength**: Begin with α=1.0, tune based on downstream performance
4. **Validation**: Always validate steering effects on held-out data
5. **Layer Selection**: Test different layers to find optimal intervention points

## Troubleshooting

### Common Issues

1. **No features selected**: Check if expectation differences are too small
2. **Weak steering effect**: Increase α or select more features
3. **Too strong intervention**: Decrease α or use fewer features
4. **Memory issues**: Reduce batch size or use fewer processes for MI calculation

### Performance Tips

1. Use GPU for SAE operations when possible
2. Adjust `num_processes` based on available CPU cores
3. Consider feature caching for repeated experiments
4. Use mixed precision if memory is limited
