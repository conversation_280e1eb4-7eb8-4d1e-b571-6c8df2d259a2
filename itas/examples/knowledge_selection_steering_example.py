#!/usr/bin/env python3
"""
Example script demonstrating Knowledge Selection Steering.

This script shows how to use the new KnowledgeSelectionSteering class
to identify SAE features responsible for knowledge selection behaviors
and apply targeted interventions.
"""

import torch
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Main example function."""
    
    # Example 1: Basic usage with synthetic data
    logger.info("=== Knowledge Selection Steering Example ===")
    
    # Load or create SAE (this is a placeholder - replace with actual SAE loading)
    from itas.core.sae import SAE
    
    # Create a simple SAE for demonstration
    sae = SAE(
        d_in=512,
        d_sae=512 * 16,  # 16x expansion
        architecture="standard",
        activation_fn="relu",
        normalize_decoder=True,
        device="cuda" if torch.cuda.is_available() else "cpu",
    )
    
    # Initialize knowledge selection steering
    from itas.analysis.representation_engineer import KnowledgeSelectionSteering
    
    steering = KnowledgeSelectionSteering(
        sae=sae,
        device="cuda" if torch.cuda.is_available() else "cpu",
        num_processes=4,
    )
    
    # Example 2: Generate synthetic hidden states for demonstration
    logger.info("Generating synthetic hidden states...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Simulate hidden states when model uses contextual knowledge
    # These would typically come from model activations when answering based on context
    contextual_hiddens = torch.randn(100, 512, device=device)
    contextual_hiddens += 0.1 * torch.randn(100, 512, device=device)  # Add some noise
    
    # Simulate hidden states when model uses parametric knowledge  
    # These would typically come from model activations when answering from memory
    parametric_hiddens = torch.randn(100, 512, device=device)
    parametric_hiddens -= 0.1 * torch.randn(100, 512, device=device)  # Different pattern
    
    logger.info(f"Contextual hiddens shape: {contextual_hiddens.shape}")
    logger.info(f"Parametric hiddens shape: {parametric_hiddens.shape}")
    
    # Example 3: Calculate mutual information
    logger.info("Calculating mutual information between SAE features and knowledge selection...")
    
    mi_result = steering.calculate_mutual_information(
        contextual_hiddens=contextual_hiddens,
        parametric_hiddens=parametric_hiddens,
        top_k_proportion=0.1,  # Select top 10% of features
        minmax_normalization=True,
        equal_label_examples=True,
        seed=42,
    )
    
    logger.info(f"Selected {len(mi_result.contextual_features)} contextual features")
    logger.info(f"Selected {len(mi_result.parametric_features)} parametric features")
    logger.info(f"Top-k features: {len(mi_result.top_k_features)}")
    
    # Example 4: Create functional activations
    logger.info("Creating functional activations for knowledge steering...")
    
    functional_activations = steering.create_functional_activations(mi_result)
    
    logger.info(f"z_C norm: {functional_activations.z_contextual.norm().item():.4f}")
    logger.info(f"z_M norm: {functional_activations.z_parametric.norm().item():.4f}")
    
    # Example 5: Apply knowledge steering
    logger.info("Applying knowledge steering interventions...")
    
    # Create some test hidden states
    test_hiddens = torch.randn(5, 10, 512, device=device)  # [batch, seq, hidden]
    
    # Steer toward parametric knowledge
    parametric_steered = steering.apply_knowledge_steering(
        hidden_states=test_hiddens,
        steering_direction="parametric",
        alpha=1.0,
        functional_activations=functional_activations,
    )
    
    # Steer toward contextual knowledge
    contextual_steered = steering.apply_knowledge_steering(
        hidden_states=test_hiddens,
        steering_direction="contextual", 
        alpha=1.0,
        functional_activations=functional_activations,
    )
    
    logger.info(f"Original hiddens norm: {test_hiddens.norm().item():.4f}")
    logger.info(f"Parametric steered norm: {parametric_steered.norm().item():.4f}")
    logger.info(f"Contextual steered norm: {contextual_steered.norm().item():.4f}")
    
    # Example 6: Create intervention functions for model hooks
    logger.info("Creating intervention functions...")
    
    parametric_intervention = steering.create_intervention_function(
        steering_direction="parametric",
        alpha=1.0,
        functional_activations=functional_activations,
    )
    
    contextual_intervention = steering.create_intervention_function(
        steering_direction="contextual",
        alpha=0.5,  # Weaker intervention
        functional_activations=functional_activations,
    )
    
    logger.info("Intervention functions created successfully")
    
    # Example 7: Feature analysis
    logger.info("Analyzing selected features...")
    
    analysis = steering.get_feature_analysis(mi_result)
    
    logger.info(f"Total features: {analysis['total_features']}")
    logger.info(f"Top-k features: {analysis['top_k_features']}")
    logger.info(f"Contextual features: {analysis['contextual_features']}")
    logger.info(f"Parametric features: {analysis['parametric_features']}")
    logger.info(f"MI score mean: {analysis['mi_score_stats']['mean']:.6f}")
    logger.info(f"Expectation mean: {analysis['expectation_stats']['mean']:.6f}")
    
    # Example 8: Save and load results
    logger.info("Saving results...")
    
    save_path = "knowledge_steering_results.pt"
    steering.save_results(save_path, mi_result, functional_activations)
    
    # Load results
    logger.info("Loading results...")
    loaded_steering, loaded_mi, loaded_functional = KnowledgeSelectionSteering.load_results(
        load_path=save_path,
        sae=sae,
        device=device,
    )
    
    logger.info("Results loaded successfully")
    
    # Clean up
    Path(save_path).unlink(missing_ok=True)
    
    logger.info("=== Example completed successfully! ===")


if __name__ == "__main__":
    main()
