# SAE Activation Classifier

The SAE Activation Classifier is a powerful tool for detecting behavioral conflicts using Sparse Autoencoder (SAE) feature activations. It uses logistic regression to classify whether a given activation pattern corresponds to a conflict scenario.

## Overview

The classifier takes SAE feature activations as input and predicts whether the activation pattern indicates a behavioral conflict. This is particularly useful for:

- **Behavioral Analysis**: Understanding when models exhibit conflicting behaviors
- **Safety Research**: Detecting potentially problematic activation patterns
- **Interpretability**: Identifying which SAE features are most important for conflict detection
- **Model Monitoring**: Real-time detection of concerning behavioral patterns

## Architecture Design

### Best Architecture Recommendations

Based on the design and existing research, here are the optimal architecture choices:

#### 1. **Input Layer**
- **Input**: SAE feature activations (typically 32x-128x expansion of hidden dimension)
- **Preprocessing**: Optional L2 normalization of features
- **Dimensionality**: Usually 100k-500k features for large models

#### 2. **Model Architecture**
- **Primary Choice**: **Logistic Regression** with L1 regularization
  - Simple, interpretable, works well with sparse features
  - L1 regularization encourages feature selection
  - Fast training and inference
  - Provides feature importance directly through weights

#### 3. **Regularization**
- **L1 Coefficient**: 1e-3 to 1e-5 (adjust based on sparsity needs)
- **Early Stopping**: Patience of 5-10 epochs
- **Dropout**: Optional (0.1-0.2) for very large feature spaces

#### 4. **Training Configuration**
- **Learning Rate**: 1e-3 to 1e-2 (Adam optimizer)
- **Batch Size**: 32-128 (depending on memory)
- **Epochs**: 50-200 with early stopping
- **Validation Split**: 20% for hyperparameter tuning

#### 5. **Evaluation Metrics**
- **Primary**: AUC-ROC (handles class imbalance well)
- **Secondary**: AUC-PR (precision-recall curve)
- **Interpretability**: Feature importance analysis

## Usage Examples

### Basic Usage

```python
from itas.analysis import SAEActivationClassifier, TrainingConfig
from itas.core.sae import SAE

# Load pre-trained SAE
sae = SAE.load("path/to/sae_model.pt")

# Configure training
config = TrainingConfig(
    learning_rate=0.001,
    num_epochs=100,
    l1_coefficient=1e-3,
    batch_size=64
)

# Create classifier
classifier = SAEActivationClassifier(sae, config)

# Train on your data
result = classifier.train(conflict_activations, no_conflict_activations)

print(f"Accuracy: {result.accuracy:.4f}")
print(f"AUC: {result.auc:.4f}")
```

### Cross-Validation

```python
# Perform 5-fold cross-validation
cv_results = classifier.cross_validate(
    conflict_activations, 
    no_conflict_activations, 
    n_folds=5
)

# Summarize results
summary = SAEActivationClassifier.summarize_cv_results(cv_results)
print(f"Mean AUC: {summary['auc_mean']:.4f} ± {summary['auc_std']:.4f}")
```

### Feature Importance Analysis

```python
# Get feature importance
importance = classifier.get_feature_importance(top_k=50)

print(f"Top 10 most important features: {importance['top_features'][:10]}")
print(f"Feature sparsity: {importance['sparsity']:.4f}")
```

### Making Predictions

```python
# Predict on new activations
predictions, probabilities = classifier.predict_from_activations(
    new_activations, 
    return_probabilities=True
)

# Binary predictions (0 or 1)
print(f"Predictions: {predictions}")

# Confidence scores (0 to 1)
print(f"Probabilities: {probabilities}")
```

## Advanced Features

### Custom Training Configuration

```python
config = TrainingConfig(
    learning_rate=0.002,
    num_epochs=150,
    batch_size=128,
    l1_coefficient=5e-4,
    early_stopping_patience=15,
    validation_split=0.25,
    device="cuda:0"
)
```

### Utility Functions

```python
# Create classifier from SAE path
classifier = create_classifier_from_sae("path/to/sae.pt")

# Train with automatic cross-validation
classifier, results = train_conflict_classifier(
    sae, 
    conflict_acts, 
    no_conflict_acts,
    cross_validate=True,
    n_folds=5
)
```

### Save and Load Models

```python
# Save trained classifier
classifier.save_model("my_classifier.pt")

# Load classifier
new_classifier = SAEActivationClassifier(sae)
new_classifier.load_model("my_classifier.pt")
```

## Performance Optimization

### Memory Efficiency
- Use batch processing for large datasets
- Consider feature selection for very high-dimensional SAEs
- Use mixed precision training if available

### Training Speed
- Start with smaller L1 coefficients for faster convergence
- Use early stopping to avoid overtraining
- Consider reducing SAE feature dimension if interpretability allows

### Accuracy Improvements
- Ensure balanced datasets or use appropriate class weights
- Experiment with different L1 coefficients
- Use cross-validation for robust hyperparameter selection
- Consider ensemble methods for critical applications

## Integration with ITAS

The classifier integrates seamlessly with other ITAS components:

```python
# Use with function extraction
from itas.analysis import FunctionExtractor

extractor = FunctionExtractor(sae)
function_result = extractor.extract_function(target_acts, context_acts)

# Train classifier on extracted features
classifier_result = classifier.train(target_acts, context_acts)
```

## Best Practices

1. **Data Quality**: Ensure your conflict/no-conflict labels are accurate
2. **Feature Engineering**: Consider normalizing SAE features
3. **Validation**: Always use cross-validation for robust evaluation
4. **Interpretability**: Analyze feature importance to understand decisions
5. **Monitoring**: Track performance metrics during training
6. **Reproducibility**: Set random seeds for consistent results

## Troubleshooting

### Common Issues

1. **Poor Performance**: Check data quality, try different L1 coefficients
2. **Overfitting**: Increase L1 regularization, reduce epochs
3. **Memory Issues**: Reduce batch size, use gradient accumulation
4. **Slow Training**: Reduce feature dimension, use smaller learning rate

### Performance Expectations

- **Good AUC**: > 0.8 for well-separated behavioral patterns
- **Acceptable AUC**: 0.7-0.8 for subtle behavioral differences  
- **Poor AUC**: < 0.7 may indicate insufficient signal or poor data quality

The classifier provides a robust foundation for behavioral conflict detection using SAE activations, with extensive customization options for different use cases.
